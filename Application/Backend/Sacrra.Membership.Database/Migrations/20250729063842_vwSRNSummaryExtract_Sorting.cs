using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class vwSRNSummaryExtract_Sorting : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
				ALTER VIEW [dbo].[vwSRNSummaryExtract] 
				AS	
					WITH branchlocations
					AS
					(
						SELECT 
							[SRNId]
							,[BranchLocations] = STRING_AGG(bra.[Name],',')
						FROM [dbo].[BranchLocations] bra
						GROUP BY [SRNId]
					)
					SELECT
						[CompanyRegistrationNumber] = ISNULL(mem.[RegisteredNumber], mem.[IdNumber])
						,[MemberName] = mem.[RegisteredName]
						,[SRN] = srn.[SRNNumber]
						,[IsLatestHistory] = CASE WHEN srnSH.[MaxId] = srnSH.[Id] THEN 'YES' ELSE 'NO' END
						,[MemberId] = srn.[MemberId]
						,[SPNumber] = spg.[SPNumber]
						,[SRNDisplayName] = srn.[TradingName]
						,[CreditInformationClassification] = cla.[Name]
						,[PortfolioManager] = shm.[FirstName] + ' ' + shm.[LastName]
						,[AccountType] = acc.[Name]
						,[NCRReportingAccountTypeClassification] = ncr.[Name]
						,[BillingCycleDay] = srn.[BillingCycleDay]
						,[SRNALGLeader] = alg.[RegisteredName]
						,[SRNALGLeaderId] = alg.[Id]
						,[LoanManagementSystemVendor] = loa.[Name]
						,[BranchLocations] = bra.[BranchLocations]
						,[NumberMonthlyRecords] = 1
						,[SRNCreationDate] = srn.[CreationDate]
						,[Status] = srnStatuses.[Name]
						,[StatusDate] = srnSH.[StatusDate]
						,[StatusReason] = srnStatusReasons.[Name]
						,[StatusComment] = srnSH.[StatusComment]
						,[LastSubmissionDate] = srnSH.[LastSubmissionDate]
						,[BureauClosureDate] = srnSH.[BureauClosureDate]
						,[BureauClosureInstruction] = srnSH.[BureauClosureInstruction]
						,[ThirdPartyVendor] = sv.[Name]
					FROM [dbo].[SRNs] srn


					INNER JOIN [dbo].[Members] mem
					ON srn.[MemberId] = mem.[Id]

					INNER JOIN [dbo].[Users] shm
					ON mem.[StakeholderManagerId] = shm.[Id]

					INNER JOIN [dbo].[AccountTypes] acc
					ON srn.[AccountTypeId] = acc.[Id]

					INNER JOIN [dbo].[NCRReportingAccountTypeClassifications] ncr
					ON srn.[NCRReportingAccountTypeClassificationId] = ncr.[Id]
																
					LEFT JOIN
					(
						SELECT
							[MaxId] = MAX([Id]) OVER (PARTITION BY [SrnId])
							,[Id]
							,[StatusDate]
							,[StatusComment]
							,[LastSubmissionDate]
							,[BureauClosureDate]
							,[BureauClosureInstruction]
							,[SrnId]
							,[StatusId]
							,[StatusReasonId]
						FROM [dbo].[SrnStatusHistory]
					) srnSH
					ON srnSH.[SrnId] = srn.[Id]

					LEFT OUTER JOIN [dbo].[SRNStatuses] srnStatuses
					ON srnStatuses.[Id] = srnSH.[StatusId]

					LEFT JOIN [dbo].[SRNStatusReasons] srnStatusReasons
					ON srnStatusReasons.[Id] = srnSH.[StatusReasonId]

					LEFT OUTER JOIN [dbo].[CreditInformationClassifications] cla
					ON srn.[CreditInformationClassificationId] = cla.[Id]

					LEFT OUTER JOIN [dbo].[SPGroups] spg
					ON srn.[SPGroupId] = spg.[Id]

					LEFT OUTER JOIN [dbo].[Members] alg
					ON srn.[ALGLeaderId] = alg.[Id]

					LEFT OUTER JOIN [dbo].[LoanManagementSystemVendors] loa
					ON srn.[LoanManagementSystemVendorId] = loa.[Id]

					LEFT OUTER JOIN branchlocations bra
					ON srn.[Id] = bra.[SRNId]

					LEFT OUTER JOIN SoftwareVendors sv
					ON srn.[SoftwareVendorId] = sv.[Id]

					ORDER BY 
						srn.[SRNNumber] ASC,
						srnSH.[Id] ASC
					OFFSET 0 ROWS
				GO");

            migrationBuilder.Sql(@"
				ALTER VIEW [dbo].[vwSRNFileExtract] 
				AS
				WITH history
				AS
				(
					SELECT
						[Id]
						,[MaxId] = MAX([Id]) OVER (PARTITION BY [SRNId], [FileType])
						,[DailyFileTestEndDate]
						,[DailyFileGoLiveDate]
						,[MonthlyFileTestEndDate]
						,[MonthlyFileGoLiveDate]
						,[BureauInstruction]
						,[LastSubmissionDate]
						,[DateCreated]
						,[SRNId]
						,[IsComple]
						,[DateCompleted]
						,[FileType]
						,[SRNStatusId]
						,[SRNStatusReasonId]
						,[SignoffDate]
						,[SRNFileTestingStatusReason]
					FROM [dbo].[SRNStatusUpdateHistory]
				)
				SELECT
					srn.[SRNNumber]
					,srn.[MemberId]
					,srn.[ALGLeaderId]
					,[SRNDisplayName] = srn.[TradingName]
					,files.[Id]
					,[CurrentSRNStatus] = sta.[Name]
					,[SRNCreationDate] = srn.[CreationDate]
					,[SRNFileType] = CASE srn.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' WHEN 3 THEN 'Daily & Monthly' END 
					,files.[IsLatestRecord]
					,files.[FileType]
					,files.[FileStatus]
					,files.[FileStatusDate]
					,files.[TestFileStatusReason]
					,files.[PlannedTestEndDate]
					,files.[FileTestSignoffDate]
					,files.[PlannedGoLiveDate]
					,files.[ActualGoLiveDate]
					,files.[TestingSkipped]
				FROM
				(
				SELECT
						tst.[Id]
						,[SRNId]
					,[FileType] = CASE tst.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' END
					,[FileStatus] = 'Test'
					,[IsLatestRecord] = CASE WHEN tst.[MaxId] = tst.[Id] AND [IsComple] = 0 THEN 'Yes' ELSE 'No' END
					,[FileStatusDate] = tst.[DateCreated]
					,[TestFileStatusReason] = rea.[Name]
					,[PlannedTestEndDate] = CASE tst.[FileType] WHEN 1 THEN tst.[DailyFileTestEndDate] WHEN 2 THEN tst.[MonthlyFileTestEndDate] END
					,[FileTestSignoffDate] = tst.[SignoffDate]
					,[PlannedGoLiveDate] = CASE tst.[FileType] WHEN 1 THEN tst.[DailyFileGoLiveDate] WHEN 2 THEN tst.[MonthlyFileGoLiveDate] END
					,[ActualGoLiveDate] = tst.[DateCompleted]
					,[TestingSkipped] = CASE WHEN FORMAT(tst.[DateCreated],'yyyy-MM-dd HH:mm:ss') > ISNULL(FORMAT(tst.[DateCompleted],'yyyy-MM-dd HH:mm:ss'),'2000-01-01') THEN 'Yes' ELSE 'No' END
				FROM history tst
				LEFT OUTER JOIN [dbo].[SRNStatusReasons] rea
				ON tst.[SRNStatusReasonId] = rea.[Id]
				WHERE tst.[FileType] IN (1,2)
				UNION ALL
				SELECT
					liv.[Id]
					,[SRNId]
					,[FileType] = CASE liv.[FileType] WHEN 1 THEN 'Daily' WHEN 2 THEN 'Monthly' END
					,[FileStatus] = 'Live'
					,[IsLatestRecord] = CASE WHEN liv.[MaxId] = liv.[Id] AND [IsComple] = 1 THEN 'Yes' ELSE 'No' END
					,[FileStatusDate] = liv.[DateCompleted]
					,[TestFileStatusReason] = NULL
					,[PlannedTestEndDate] = CASE liv.[FileType] WHEN 1 THEN liv.[DailyFileTestEndDate] WHEN 2 THEN liv.[MonthlyFileTestEndDate] END
					,[FileTestSignoffDate] = liv.[SignoffDate]
					,[PlannedGoLiveDate] = CASE liv.[FileType] WHEN 1 THEN liv.[DailyFileGoLiveDate] WHEN 2 THEN liv.[MonthlyFileGoLiveDate] END
					,[ActualGoLiveDate] = liv.[DateCompleted]
					,[TestingSkipped] = CASE WHEN FORMAT(liv.[DateCreated],'yyyy-MM-dd HH:mm:ss') > ISNULL(FORMAT(liv.[DateCompleted],'yyyy-MM-dd HH:mm:ss'),'2000-01-01') THEN 'Yes' ELSE 'No' END
				FROM history liv
				LEFT OUTER JOIN [dbo].[SRNStatusReasons] rea
				ON liv.[SRNStatusReasonId] = rea.[Id]
				WHERE liv.[IsComple] = 1
				AND liv.[FileType] IN (1,2)
				) files
				INNER JOIN [dbo].[SRNs] srn
				ON files.[SRNId] = srn.[Id]
				INNER JOIN [dbo].[SRNStatuses] sta
				ON srn.[SRNStatusId] = sta.[Id]

				ORDER BY
					srn.[SRNNumber] ASC,
					Id ASC
				OFFSET 0 ROWS

				GO");

            migrationBuilder.Sql(@"
				ALTER VIEW [dbo].[vwSRNComplianceContacts]
				AS
					SELECT 
						srn.[SRNNumber] AS SRNNumber
						,srn.[MemberId] AS MemberId
						,srn.[ALGLeaderId] AS ALGLeaderId
						,cc.[FirstName] AS ComplianceContactFirstName
						,cc.[Surname] AS ComplianceContactSurname
						,cc.[Email] AS ComplianceContactEmail
						,cc.[JobTitle] AS ComplianceContactJobTitle
						,cc.[CellNumber] AS ComplianceContactCellNumber
						,cc.[OfficeTelNumber] AS ComplianceContactOfficeNumber
					FROM SRNContacts cc

					INNER JOIN SRNs srn
					ON srn.[Id] = cc.[SRNId]
					WHERE cc.[ContactTypeId] = 4

					ORDER BY
						srn.[SRNNumber] ASC,
						cc.[Id] ASC
					OFFSET 0 ROWS

				GO");

            migrationBuilder.Sql(@"
				ALTER VIEW [dbo].[vwSRNDataContacts]
				AS
					SELECT 
						srn.[SRNNumber] AS SRNNumber
						,srn.[MemberId] AS MemberId
						,srn.[ALGLeaderId] AS ALGLeaderId
						,dc.[FirstName] AS DataContactFirstName
						,dc.[Surname] AS DataContactSurname
						,dc.[Email] AS DataContactEmail
						,dc.[JobTitle] AS DataContactJobTitle
						,dc.[CellNumber] AS DataContactCellNumber
						,dc.[OfficeTelNumber] AS DataContactOfficeNumber
					FROM SRNContacts dc

					INNER JOIN SRNs srn
					ON srn.[Id] = dc.[SRNId]
					WHERE dc.[ContactTypeId] = 5

					ORDER BY
						srn.[SRNNumber] ASC,
						dc.[Id] ASC
					OFFSET 0 ROWS

				GO");

            migrationBuilder.Sql(@"
				ALTER VIEW [dbo].[vwSRNManualAmendmentsContacts]
				AS
					SELECT 
						srn.[SRNNumber] AS SRNNumber
						,srn.[MemberId] AS MemberId
						,srn.[ALGLeaderId] AS ALGLeaderId
						,ma.[FirstName] AS ManualAmendmentsContactFirstName
						,ma.[Surname] AS ManualAmendmentsContactSurname
						,ma.[Email] AS ManualAmendmentsContactEmail
						,ma.[JobTitle] AS ManualAmendmentsContactJobTitle
						,ma.[CellNumber] AS ManualAmendmentsContactCellNumber
						,ma.[OfficeTelNumber] AS ManualAmendmentsContactOfficeNumber
					FROM SRNContacts ma

					INNER JOIN SRNs srn
					ON srn.[Id] = ma.[SRNId]
					WHERE ma.[ContactTypeId] = 6

					ORDER BY
						srn.[SRNNumber] ASC,
						ma.[Id] ASC
					OFFSET 0 ROWS

				GO");

            migrationBuilder.Sql(@"
				ALTER VIEW [dbo].[vwSRNDTHContacts]
				AS
					SELECT 
						srn.[SRNNumber] AS SRNNumber
						,srn.[MemberId] AS MemberId
						,srn.[ALGLeaderId] AS ALGLeaderId
						,dth.[FirstName] AS DTHContactFirstName
						,dth.[Surname] AS DTHContactSurname
						,dth.[Email] AS DTHContactEmail
						,dth.[JobTitle] AS DTHContactJobTitle
						,dth.[CellNumber] AS DTHContactCellNumber
						,dth.[OfficeTelNumber] AS DTHContactOfficeNumber
					FROM SRNContacts dth

					INNER JOIN SRNs srn
					ON srn.[Id] = dth.[SRNId]
					WHERE dth.[ContactTypeId] = 7

					ORDER BY
						srn.[SRNNumber] ASC,
						dth.[Id] ASC
					OFFSET 0 ROWS

				GO");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNSummaryExtract]");
            migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNFileExtract]");
            migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNComplianceContacts]");
            migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNDataContacts]");
            migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNManualAmendmentsContacts]");
            migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNDTHContacts]");
        }
    }
}
