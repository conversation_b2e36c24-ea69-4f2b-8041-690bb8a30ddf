using System;
using System.Globalization;
using Microsoft.EntityFrameworkCore;

namespace Sacrra.Membership.Database.Models
{
    [Keyless]
    public class vwSRNFileExtract
    {
        public string SRNNumber { get; set; }
        public int MemberId { get; set; }
        public int? ALGLeaderId { get; set; }
        public string SRNDisplayName { get; set; }
        public string CurrentSRNStatus { get; set; }
        public DateTime? SRNCreationDate { get; set; }
        public string SRNFileType { get; set; }
        public string IsLatestRecord { get; set; }
        public string FileType { get; set; }
        public string FileStatus { get; set; }
        public DateTime? FileStatusDate { get; set; }
        public string TestFileStatusReason { get; set; }
        public DateTime? PlannedTestEndDate { get; set; }
        public DateTime? FileTestSignoffDate { get; set; }
        public DateTime? PlannedGoLiveDate { get; set; }
        public DateTime? ActualGoLiveDate { get; set; }
        public string TestingSkipped { get; set; }
    }
}
