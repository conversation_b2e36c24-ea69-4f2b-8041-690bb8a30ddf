<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <!--[if gte mso 9]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width">
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <title></title>
    <!--[if !mso]><!-->
    <!--<![endif]-->
    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
        }

        table,
        td,
        tr {
            vertical-align: top;
            border-collapse: collapse;
        }

        * {
            line-height: inherit;
        }

        a[x-apple-data-detectors=true] {
            color: inherit !important;
            text-decoration: none !important;
        }

        .ie-browser table {
            table-layout: fixed;
        }

        [owa] .img-container div,
        [owa] .img-container button {
            display: block !important;
        }

        [owa] .fullwidth button {
            width: 100% !important;
        }

        [owa] .block-grid .col {
            display: table-cell;
            float: none !important;
            vertical-align: top;
        }

        .ie-browser .block-grid,
        .ie-browser .num12,
        [owa] .num12,
        [owa] .block-grid {
            width: 600px !important;
        }

        .ie-browser .mixed-two-up .num4,
        [owa] .mixed-two-up .num4 {
            width: 200px !important;
        }

        .ie-browser .mixed-two-up .num8,
        [owa] .mixed-two-up .num8 {
            width: 400px !important;
        }

        .ie-browser .block-grid.two-up .col,
        [owa] .block-grid.two-up .col {
            width: 300px !important;
        }

        .ie-browser .block-grid.three-up .col,
        [owa] .block-grid.three-up .col {
            width: 300px !important;
        }

        .ie-browser .block-grid.four-up .col [owa] .block-grid.four-up .col {
            width: 150px !important;
        }

        .ie-browser .block-grid.five-up .col [owa] .block-grid.five-up .col {
            width: 120px !important;
        }

        .ie-browser .block-grid.six-up .col,
        [owa] .block-grid.six-up .col {
            width: 100px !important;
        }

        .ie-browser .block-grid.seven-up .col,
        [owa] .block-grid.seven-up .col {
            width: 85px !important;
        }

        .ie-browser .block-grid.eight-up .col,
        [owa] .block-grid.eight-up .col {
            width: 75px !important;
        }

        .ie-browser .block-grid.nine-up .col,
        [owa] .block-grid.nine-up .col {
            width: 66px !important;
        }

        .ie-browser .block-grid.ten-up .col,
        [owa] .block-grid.ten-up .col {
            width: 60px !important;
        }

        .ie-browser .block-grid.eleven-up .col,
        [owa] .block-grid.eleven-up .col {
            width: 54px !important;
        }

        .ie-browser .block-grid.twelve-up .col,
        [owa] .block-grid.twelve-up .col {
            width: 50px !important;
        }

        .tableInfo {
                width: 100%;
                font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                border-collapse: collapse;
            }

            .tableInfoRow {
                background: #40454B;
            }

            .tableInfoTitle {
                width: 220px; 
                font-size: 14px; 
                font-weight: 700;
                text-align: end;
                padding:5px; 
                color: #fff;
         
            }

            .tableInfoItem {
                width: 335px;
                text-align: start;
                padding: 5px; 
                color: #fff;
                
            }

    </style>
    <style type="text/css" id="media-query">
        @media only screen and (min-width: 620px) {
            .block-grid {
                width: 600px !important;
            }

            .block-grid .col {
                vertical-align: top;
            }

            .block-grid .col.num12 {
                width: 600px !important;
            }

            .block-grid.mixed-two-up .col.num3 {
                width: 150px !important;
            }

            .block-grid.mixed-two-up .col.num4 {
                width: 200px !important;
            }

            .block-grid.mixed-two-up .col.num8 {
                width: 400px !important;
            }

            .block-grid.mixed-two-up .col.num9 {
                width: 450px !important;
            }

            .block-grid.two-up .col {
                width: 300px !important;
            }

            .block-grid.three-up .col {
                width: 200px !important;
            }

            .block-grid.four-up .col {
                width: 150px !important;
            }

            .block-grid.five-up .col {
                width: 120px !important;
            }

            .block-grid.six-up .col {
                width: 100px !important;
            }

            .block-grid.seven-up .col {
                width: 85px !important;
            }

            .block-grid.eight-up .col {
                width: 75px !important;
            }

            .block-grid.nine-up .col {
                width: 66px !important;
            }

            .block-grid.ten-up .col {
                width: 60px !important;
            }

            .block-grid.eleven-up .col {
                width: 54px !important;
            }

            .block-grid.twelve-up .col {
                width: 50px !important;
            }
        }

        @media (max-width: 620px) {

            .block-grid,
            .col {
                min-width: 320px !important;
                max-width: 100% !important;
                display: block !important;
            }

            .block-grid {
                width: 100% !important;
            }

            .col {
                width: 100% !important;
            }

            .col>div {
                margin: 0 auto;
            }

            img.fullwidth,
            img.fullwidthOnMobile {
                max-width: 100% !important;
            }

            .no-stack .col {
                min-width: 0 !important;
                display: table-cell !important;
            }

            .no-stack.two-up .col {
                width: 50% !important;
            }

            .no-stack .col.num4 {
                width: 33% !important;
            }

            .no-stack .col.num8 {
                width: 66% !important;
            }

            .no-stack .col.num4 {
                width: 33% !important;
            }

            .no-stack .col.num3 {
                width: 25% !important;
            }

            .no-stack .col.num6 {
                width: 50% !important;
            }

            .no-stack .col.num9 {
                width: 75% !important;
            }

            .video-block {
                max-width: none !important;
            }

            .mobile_hide {
                min-height: 0px;
                max-height: 0px;
                max-width: 0px;
                display: none;
                overflow: hidden;
                font-size: 0px;
            }

            .desktop_hide {
                display: block !important;
                max-height: none !important;
            }
        }

    </style>
</head>

<body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #fff;">
    <style type="text/css" id="media-query-bodytag">
        @media (max-width: 620px) {
            .block-grid {
                min-width: 320px !important;
                max-width: 100% !important;
                width: 100% !important;
                display: block !important;
            }

            .col {
                min-width: 320px !important;
                max-width: 100% !important;
                width: 100% !important;
                display: block !important;
            }

            .col>div {
                margin: 0 auto;
            }

            img.fullwidth {
                max-width: 100% !important;
                height: auto !important;
            }

            img.fullwidthOnMobile {
                max-width: 100% !important;
                height: auto !important;
            }

            .no-stack .col {
                min-width: 0 !important;
                display: table-cell !important;
            }

            .no-stack.two-up .col {
                width: 50% !important;
            }

            .no-stack.mixed-two-up .col.num4 {
                width: 33% !important;
            }

            .no-stack.mixed-two-up .col.num8 {
                width: 66% !important;
            }

            .no-stack.three-up .col.num4 {
                width: 33% !important
            }

            .no-stack.four-up .col.num3 {
                width: 25% !important
            }
        }

    </style>
    <!--[if IE]><div class="ie-browser"><![endif]-->
    <table class="nl-container"
           style="table-layout: fixed; vertical-align: top; min-width: 320px; Margin: 0 auto; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fff; width: 100%;"
           cellpadding="0" cellspacing="0" role="presentation" width="100%" bgcolor="#fff" valign="top">
        <tbody>
            <tr style="vertical-align: top;" valign="top">
                <td style="word-break: break-word; vertical-align: top; border-collapse: collapse;" valign="top">
                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#fff"><![endif]-->
                    <div style="background-color:transparent;">
                        <div class="block-grid "
                             style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                            <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px"><tr class="layout-full-width" style="background-color:transparent"><![endif]-->
                                <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color:transparent;width:600px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:0px; padding-bottom:0px;"><![endif]-->
                                <div class="col num12" style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                                    <div style="width:100% !important;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                             style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:0px; padding-bottom:0px; padding-right: 0px; padding-left: 0px;">
                                            <!--<![endif]-->
                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 25px; padding-right: 25px; padding-bottom: 25px; padding-left: 25px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 0px solid transparent;"
                                                                   align="center" role="presentation" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 11px solid #91c740; height: 0px;"
                                                                   align="center" role="presentation" height="0" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            height="0" valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <div class="img-container center  autowidth " align="center"
                                                style="padding-right: 0px;padding-left: 0px;">
                                              <img
                                                    class="center  autowidth " align="center" border="0"
                                                    src="data:image/png;base64,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"
                                                    alt="SACRRA_Logo" title="SACRRA_Logo"
                                                    style="padding:16px 0 ;outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; clear: both; border: 0; height: auto; float: none; width: 100%; max-width: 340px; display: block;"
                                                    width="340">
                                              
                                            </div>
                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div>
                                        <!--<![endif]-->
                                    </div>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                            </div>
                        </div>
                    </div>
                    <div style="background-color:transparent;">
                        <div class="block-grid "
                             style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;;">
                            <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
                                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
                                <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color:#FFFFFF;width:600px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:0px; padding-bottom:0px;"><![endif]-->
                                <div class="col num12" style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                                    <div style="width:100% !important;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                             style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:0px; padding-bottom:0px; padding-right: 0px; padding-left: 0px;">
                                            <!--<![endif]-->
                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 15px; padding-right: 15px; padding-bottom: 15px; padding-left: 15px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 0px solid transparent;"
                                                                   align="center" role="presentation" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 30px; padding-top: 10px; padding-bottom: 5px; font-family: Arial, sans-serif"><![endif]-->
                                            <div
                                                 style="color:#555555;font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;line-height:120%;padding-top:10px;padding-right:10px;padding-bottom:5px;padding-left:30px;">
                                                <div
                                                     style="font-size: 12px; line-height: 14px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #555555;">
                                                    <p style="font-size: 14px; line-height: 16px; margin: 0;"><strong><span
                                                                  style="font-size: 24px; line-height: 28px;">Dear [Member],</span></strong></p>
                                                    <p style="font-size: 14px; line-height: 16px; margin: 0;"> </p>
                                                    <p style="font-size: 14px; line-height: 16px; margin: 0;">
                                                        <span style="font-size: 14px; line-height: 16px;">
                                                            <span style="line-height: 16px; font-size: 14px;">Your request for the submission of a replacement
                                                                file ([FileName]) has been <strong>approved</strong>.
                                                            </span>
                                                        </span>
                                                    </p>

                                                    <p style="font-size: 14px; line-height: 16px; margin: 0;"> </p>
                                                    <p style="font-size: 14px; line-height: 16px; margin: 0;">
                                                        <span style="font-size: 14px; line-height: 16px;">
                                                            <span style="line-height: 16px; font-size: 14px;">Should you have any queries in this regard, please
                                                                send an <NAME_EMAIL> and your stakeholder manager will get back to you.
                                                            </span>
                                                        </span>
                                                    </p>
                                                </div>
                                            </div>


                                            <br>
                                            <table class="tableInfo">
                                              <tr class="tableInfoRow">
                                                <td class="tableInfoTitle">File Name:</td>
                                                <td style="width: 5px;"></td>
                                                <td class="tableInfoItem">[FileName]</td>
                                              </tr>
                                              <td  style="height: 5px;"></td>
                                              <tr class="tableInfoRow">
                                                <td class="tableInfoTitle">Replacement File Reason:</td>
                                                <td style="width: 5px;"></td>
                                                <td class="tableInfoItem">[ReplacementFileReason]</td>
                                              </tr>
                                              <td  style="height: 5px;"></td>
                                              <tr class="tableInfoRow">
                                                <td class="tableInfoTitle">Proposed Submission Date:</td>
                                                <td style="width: 5px;"></td>
                                                <td class="tableInfoItem">[SubmissionStatusDate]</td>
                                              </tr>
                                              <td  style="height: 5px;"></td>
                                              </table>
                                            

                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 15px; padding-right: 15px; padding-bottom: 15px; padding-left: 15px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 0px solid transparent;"
                                                                   align="center" role="presentation" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div>
                                        <!--<![endif]-->
                                    </div>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                            </div>
                        </div>
                    </div>
                    <div style="background-color:transparent;">
                        <div class="block-grid "
                             style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #EDEDED;;">
                            <div style="border-collapse: collapse;display: table;width: 100%;background-color:#EDEDED;">
                                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px"><tr class="layout-full-width" style="background-color:#EDEDED"><![endif]-->
                                <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color:#EDEDED;width:600px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
                                <div class="col num12" style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                                    <div style="width:100% !important;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                             style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                                            <!--<![endif]-->
                                            <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 30px; padding-left: 30px; padding-top: 30px; padding-bottom: 30px; font-family: Arial, sans-serif"><![endif]-->
                                            <div
                                                 style="color:#555555;font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;line-height:180%;padding-top:30px;padding-right:30px;padding-bottom:30px;padding-left:30px;">
                                                <div
                                                     style="font-size: 12px; line-height: 21px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #555555;">
                                                    <p style="font-size: 12px; line-height: 25px; margin: 0;"><span style="font-size: 14px;">If you think you're
                                                            not the intended recipient for this email, please delete this email.</span></p>
                                                    <p style="font-size: 12px; line-height: 21px; margin: 0;"><em><strong><span
                                                                      style="font-size: 14px; line-height: 25px;">The SACRRA Team</span></strong></em></p>
                                                </div>
                                            </div>
                                            <!--[if mso]></td></tr></table><![endif]-->
                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div>
                                        <!--<![endif]-->
                                    </div>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                            </div>
                        </div>
                    </div>
                    <div style="background-color:transparent;">
                        <div class="block-grid three-up"
                             style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #BBBBBB;;">
                            <div style="border-collapse: collapse;display: table;width: 100%;background-color:#BBBBBB;">
                                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px"><tr class="layout-full-width" style="background-color:#BBBBBB"><![endif]-->
                                <!--[if (mso)|(IE)]><td align="center" width="200" style="background-color:#BBBBBB;width:200px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:0px;"><![endif]-->
                                <div class="col num4" style="max-width: 320px; min-width: 200px; display: table-cell; vertical-align: top;;">
                                    <div style="width:100% !important;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                             style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:0px; padding-right: 0px; padding-left: 0px;">
                                            <!--<![endif]-->
                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 0px solid transparent;"
                                                                   align="center" role="presentation" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <div class="img-container center  autowidth " align="center" style="padding-right: 0px;padding-left: 0px;">
                                                <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px"><td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]-->
                                                <div style="font-size:1px;line-height:15px"> </div><img class="center  autowidth " align="center" border="0"
                                                     src="https://d1oco4z2z1fhwp.cloudfront.net/templates/default/17/icon_phone_white.png" alt="Image"
                                                     title="Image"
                                                     style="outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; clear: both; border: 0; height: auto; float: none; width: 100%; max-width: 20px; display: block;"
                                                     width="20">
                                                <!--[if mso]></td></tr></table><![endif]-->
                                            </div>
                                            <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 5px; padding-left: 5px; padding-top: 5px; padding-bottom: 5px; font-family: Arial, sans-serif"><![endif]-->
                                            <div
                                                 style="color:#FFFFFF;font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;line-height:120%;padding-top:5px;padding-right:5px;padding-bottom:5px;padding-left:5px;">
                                                <div
                                                     style="font-size: 12px; line-height: 14px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #FFFFFF;">
                                                    <p style="font-size: 14px; line-height: 16px; text-align: center; margin: 0;"><span
                                                              style="font-size: 14px; line-height: 16px;"><strong>+2787 701 3254</strong></span></p>
                                                </div>
                                            </div>
                                            <!--[if mso]></td></tr></table><![endif]-->
                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 0px solid transparent;"
                                                                   align="center" role="presentation" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div>
                                        <!--<![endif]-->
                                    </div>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                <!--[if (mso)|(IE)]></td><td align="center" width="200" style="background-color:#BBBBBB;width:200px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
                                <div class="col num4" style="max-width: 320px; min-width: 200px; display: table-cell; vertical-align: top;;">
                                    <div style="width:100% !important;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                             style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                                            <!--<![endif]-->
                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 0px solid transparent;"
                                                                   align="center" role="presentation" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <div class="img-container center  autowidth " align="center" style="padding-right: 0px;padding-left: 0px;">
                                                <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px"><td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]-->
                                                <div style="font-size:1px;line-height:10px"> </div><img class="center  autowidth " align="center" border="0"
                                                     src="https://d1oco4z2z1fhwp.cloudfront.net/templates/default/17/icon_mail_white.png" alt="Image"
                                                     title="Image"
                                                     style="outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; clear: both; border: 0; height: auto; float: none; width: 100%; max-width: 20px; display: block;"
                                                     width="20">
                                                <!--[if mso]></td></tr></table><![endif]-->
                                            </div>
                                            <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Arial, sans-serif"><![endif]-->
                                            <div
                                                 style="color:#FFFFFF;font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;line-height:120%;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                                                <div
                                                     style="font-size: 12px; line-height: 14px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #FFFFFF;">
                                                    <p style="font-size: 14px; line-height: 16px; text-align: center; margin: 0;"><span
                                                              style="font-size: 14px; line-height: 16px;"><strong><EMAIL></strong></span></p>
                                                </div>
                                            </div>
                                            <!--[if mso]></td></tr></table><![endif]-->
                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 0px solid transparent;"
                                                                   align="center" role="presentation" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div>
                                        <!--<![endif]-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="background-color:transparent;">
                        <div class="block-grid "
                             style="Margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;;">
                            <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px"><tr class="layout-full-width" style="background-color:transparent"><![endif]-->
                                <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color:transparent;width:600px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
                                <div class="col num12" style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top;;">
                                    <div style="width:100% !important;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                             style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                                            <!--<![endif]-->
                                            <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;"
                                                   role="presentation" valign="top">
                                                <tbody>
                                                    <tr style="vertical-align: top;" valign="top">
                                                        <td class="divider_inner"
                                                            style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 25px; padding-right: 25px; padding-bottom: 25px; padding-left: 25px; border-collapse: collapse;"
                                                            valign="top">
                                                            <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                   style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-top: 0px solid transparent;"
                                                                   align="center" role="presentation" valign="top">
                                                                <tbody>
                                                                    <tr style="vertical-align: top;" valign="top">
                                                                        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; border-collapse: collapse;"
                                                                            valign="top"><span></span></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div>
                                        <!--<![endif]-->
                                    </div>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                            </div>
                        </div>
                    </div>
                    <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                </td>
            </tr>
        </tbody>
    </table>
    <!--[if (IE)]></div><![endif]-->
</body>

</html>
