using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Saccra.Membership.Business.Services.DynamicService;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.DynamicControllers
{
    [Route("api/dynamic/[action]")]
    [ApiController]
    public class DynamicControllers : Controller
    {
        private DynamicService _dynamicService;

        public DynamicControllers(DynamicService dynamicService)
        {
            _dynamicService = dynamicService;
        }
        //Get Table Data
        [HttpGet("{tableName}")]
        public Dictionary<string, object>[] GetData(string tableName)
        {
            var results = _dynamicService.GetData(tableName);
            return results;
        }
       
        //Add new Data
        [HttpPost("{tableName}")]
        public void InsertRow(string tableName, [FromBody] Dictionary<string, object> data)
        {
            _dynamicService.InsertRow(tableName, data);
        }

        //Update Data
        [HttpPut("{tableName}")]
        public void UpdateRow(string tableName, [FromBody] Dictionary<string, object> data)
        {
            _dynamicService.UpdateRow(tableName, data);
        }


        //Delete Data
        [HttpDelete("{tableName}/{id}")]
        public void DeleteRow(string tableName, int id)
        {
            _dynamicService.DeleteRow(tableName, id);
        }

        //Get selected Data
        [HttpGet("{tableName}/{id}")]
        public Dictionary<string, object> GetRow(string tableName, int id)
        {
            var results = _dynamicService.GetRow(tableName, id);
            return results;
        }
    }
}
