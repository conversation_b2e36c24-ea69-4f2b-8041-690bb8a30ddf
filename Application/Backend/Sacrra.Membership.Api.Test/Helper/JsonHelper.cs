using System.Text.Json;

namespace Sacrra.Membership.Api.Test.Helper;

public class JsonHelper
{
    public static T DeserializeFile<T>(string path)
    {
        if (!File.Exists(path))
            throw new Exception($"File {path} does not exist");
        var jsonString = File.ReadAllText(path);
        return Deserialize<T>(jsonString);
    }

    private static T Deserialize<T>(string jsonString)
    {
        var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        var jsonObject = JsonSerializer.Deserialize<T>(jsonString, options);
        if (jsonObject == null)
            throw new Exception("JSON object is null");
        return jsonObject;
    }
}